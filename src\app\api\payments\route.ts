import { NextRequest } from 'next/server'

import {
  successResponse,
  errorResponse,
  with<PERSON>rror<PERSON><PERSON><PERSON>,
  handleDatabaseError,
  handleCorsPreflightRequest
} from '@/lib/api-utils'

import { supabase, withRetry } from '@/lib/supabase'
import type { PaymentsApiResponse } from '@/types'
import { roundToCurrency } from '@/utils'

// Handle CORS preflight requests
export async function OPTIONS() {
  return handleCorsPreflightRequest()
}

// GET - Fetch all customer payments with optional filtering (NO DEFAULT LIMIT)
export const GET = withErrorHandler(async (request: NextRequest) => {
  const { searchParams } = new URL(request.url)

  // Optional filters
  const search = searchParams.get('search')
  const customerName = searchParams.get('customer_name')
  const customerFamilyName = searchParams.get('customer_family_name')
  const paymentMethod = searchParams.get('payment_method')
  const dateFrom = searchParams.get('date_from')
  const dateTo = searchParams.get('date_to')

  // Optional pagination - if not specified, return ALL payments
  const page = searchParams.get('page') ? parseInt(searchParams.get('page')!) : null
  const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : null

  let query = supabase
    .from('customer_payments')
    .select('*', { count: 'exact' })
    .order('payment_date', { ascending: false })

  // Apply pagination only if specified
  if (page && limit) {
    const offset = (page - 1) * limit
    query = query.range(offset, offset + limit - 1)
  }

  // Apply search filter
  if (search) {
    query = query.or(`customer_name.ilike.%${search}%,customer_family_name.ilike.%${search}%,responsible_family_member.ilike.%${search}%`)
  }

  // Apply specific filters
  if (customerName) {
    query = query.eq('customer_name', customerName)
  }
  if (customerFamilyName) {
    query = query.eq('customer_family_name', customerFamilyName)
  }
  if (paymentMethod) {
    query = query.eq('payment_method', paymentMethod)
  }
  if (dateFrom) {
    query = query.gte('payment_date', dateFrom)
  }
  if (dateTo) {
    query = query.lte('payment_date', dateTo)
  }

  const result = await withRetry(async () => {
    const { data: payments, error, count } = await query
    if (error) throw error
    return { payments, count }
  })

  const { payments, count } = result

  // Return response with or without pagination
  const responseData: PaymentsApiResponse = {
    payments: payments || [],
  }

  if (page && limit) {
    responseData.pagination = {
      page,
      limit,
      total: count || 0,
      totalPages: Math.ceil((count || 0) / limit)
    }
  } else {
    responseData.total = count || 0
  }

  return successResponse(responseData)
})

// POST - Create new customer payment
export const POST = withErrorHandler(async (request: NextRequest) => {
  const body = await request.json()

  // Validate required fields
  if (!body.customer_name || !body.customer_family_name || !body.payment_amount) {
    return errorResponse('Missing required fields', 400)
  }

  const {
    customer_name,
    customer_family_name,
    payment_amount,
    payment_date,
    payment_method,
    responsible_family_member,
    notes
  } = body

  // Validate numeric fields
  if (typeof payment_amount !== 'number' || payment_amount <= 0) {
    return errorResponse('Payment amount must be a positive number', 400)
  }

  // Validate string lengths
  if (customer_name.length < 2 || customer_name.length > 255) {
    return errorResponse('Customer name must be between 2 and 255 characters', 400)
  }

  if (customer_family_name.length < 2 || customer_family_name.length > 255) {
    return errorResponse('Customer family name must be between 2 and 255 characters', 400)
  }

  if (responsible_family_member && (responsible_family_member.length < 2 || responsible_family_member.length > 255)) {
    return errorResponse('Responsible family member name must be between 2 and 255 characters', 400)
  }

  // Validate payment date if provided
  if (payment_date && isNaN(Date.parse(payment_date))) {
    return errorResponse('Invalid payment date format', 400)
  }

  // Validate payment method
  const validPaymentMethods = ['Cash', 'GCash', 'PayMaya', 'Bank Transfer', 'Others']
  if (payment_method && !validPaymentMethods.includes(payment_method)) {
    return errorResponse('Invalid payment method', 400)
  }

  const { data: payment, error } = await supabase
    .from('customer_payments')
    .insert([
      {
        customer_name: customer_name.trim(),
        customer_family_name: customer_family_name.trim(),
        payment_amount: roundToCurrency(Number(payment_amount)),
        payment_date: payment_date || new Date().toISOString().split('T')[0],
        payment_method: payment_method || 'Cash',
        responsible_family_member: responsible_family_member?.trim() || null,
        notes: notes?.trim() || null
      }
    ])
    .select()
    .single()

  if (error) {
    return handleDatabaseError(error)
  }

  return successResponse({ payment }, 'Payment recorded successfully', 201)
})
